<div class="bg-white border border-gray-200 rounded-lg overflow-hidden">
    @if(count($fields) > 0)
        <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <h3 class="text-lg font-medium text-gray-900">Dostępne pola faktury</h3>
            <p class="mt-1 text-sm text-gray-600">
                Lista wszystkich pól, które mogą być umieszczone na fakturze.
                @if($selectedTemplate)
                    Pola oznaczone zielonym są obsługiwane przez wybrany szablon.
                @endif
            </p>
        </div>

        <div class="divide-y divide-gray-200">
            @foreach($fields as $field)
                @php
                    $isSupported = $selectedTemplate && in_array($field['field_name'], $selectedTemplate['supported_fields'] ?? []);
                    $isSelected = isset($selectedFields) && in_array($field['field_name'], $selectedFields ?? []);
                @endphp

                <div class="px-6 py-4 {{ $isSupported ? 'bg-green-50' : '' }}">
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <div class="flex items-center">
                                <h4 class="text-sm font-medium text-gray-900">
                                    {{ $field['display_name'] }}
                                </h4>
                                @if($isSupported)
                                    <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                        Obsługiwane
                                    </span>
                                @endif
                                @if($isSelected)
                                    <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                        Wybrane
                                    </span>
                                @endif
                            </div>
                            <p class="mt-1 text-sm text-gray-600">{{ $field['description'] }}</p>
                            <p class="mt-1 text-xs text-gray-500 font-mono">{{ $field['field_name'] }}</p>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>

        @if($selectedTemplate)
            <div class="px-6 py-4 bg-blue-50 border-t border-gray-200">
                <div class="flex items-center">
                    <svg class="w-5 h-5 text-blue-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                    </svg>
                    <div class="text-sm">
                        <p class="text-blue-800 font-medium">
                            Wybrany szablon obsługuje {{ count($selectedTemplate['supported_fields'] ?? []) }} z {{ count($fields) }} dostępnych pól.
                        </p>
                    </div>
                </div>
            </div>
        @endif
    @else
        <div class="px-6 py-8 text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V9a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">Brak dostępnych pól</h3>
            <p class="mt-1 text-sm text-gray-500">Nie znaleziono definicji pól w pliku konfiguracyjnym szablonów.</p>
        </div>
    @endif
</div>
