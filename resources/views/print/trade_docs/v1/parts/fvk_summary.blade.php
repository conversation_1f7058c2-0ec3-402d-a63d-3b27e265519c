<table class="summary">
{{--    <tr>--}}
{{--        <td><strong>Zap<PERSON>cono:</strong></td>--}}
{{--        <td colspan="2">--}}
{{--            @if($record->is_paid)--}}
{{--                {{number_format($vat['summary']['gross_amount'] ?? 0, 2, ',', '')}} {{$record->currency}},--}}
{{--                {{$record->payment_date->format('Y-m-d')}}--}}
{{--            @else--}}
{{--                0,00 {{$record->currency}}--}}
{{--            @endif--}}
{{--        </td>--}}
{{--    </tr>--}}
    <tr>
        @if($vat['summary']['gross_amount'] > 0)
        <td><strong>Do zapłaty:</strong></td>
        @else
        <td><strong>Do zwrotu:</strong></td>
        @endif

        <td colspan="2">
                {{number_format(abs($vat['summary']['gross_amount'] ?? 0), 2, ',', '')}} {{$record->currency}}
        </td>
    </tr>
{{--    <tr>--}}
{{--        <td><strong>Słownie:</strong></td>--}}
{{--        <td colspan="2">{{\App\Helpers\NumberToWords::get(abs($vat['summary']['gross_amount'] ?? 0), '.')}}</td>--}}
{{--    </tr>--}}
</table>
<table class="summary" width="80mm;">
    <tr>
        <td style="width: 4cm;">
            <strong>Przyczyna korekty: </strong>
        </td>
        <td>
            {!! $record->notes !!}
        </td>
    </tr>
</table>
