<table class="items" style="width:190mm;">
        <!-- Nagłówki tabeli -->
        <tr class="heading">
            <td style="width: 73mm;">Opis usługi</td>
            <td style="width: 13mm;"><PERSON><PERSON><PERSON><PERSON></td>
            <td style="width: 13mm;">j.m.</td>
            @if($record->vat_method === \App\Enums\TradeDocVatMethod::BASE_ON_NET)
            <td style="width: 23mm;">Cena netto</td>
            @else
            <td style="width: 23mm;">Cena brutto</td>
            @endif
            <td style="width: 13mm;">Rabat</td>
            <td style="width: 8mm;">VAT</td>
            @if($record->vat_method === \App\Enums\TradeDocVatMethod::BASE_ON_NET)
            <td style="width: 28mm;">Wart. netto</td>
            @else
            <td style="width: 28mm;">Wart. brutto</td>
            @endif
        </tr>
@foreach($record->items as $item)
    <tr class="item">
        <td>{{$item->label}}</td>
        <td>{{number_format($item->amount, 3, ',', '')}}</td>
        <td>{{$item->unit_type}}</td>
        @if($record->vat_method === \App\Enums\TradeDocVatMethod::BASE_ON_NET)
        <td>{{$item->net_unit_price}}</td>
        @else
        <td>{{$item->gross_unit_price}}</td>
        @endif
        <td>{{$item->discount_value}}</td>
        <td>{{$item->vat_label}}</td>
        @if($record->vat_method === \App\Enums\TradeDocVatMethod::BASE_ON_NET)
        <td>{{$item->net_value}}</td>
        @else
        <td>{{$item->gross_value}}</td>
        @endif
    </tr>
@endforeach
<!-- Podsumowanie -->
<tr class="total">
    <td colspan="3"></td>
    <td colspan="3"><strong>Wartość netto:</strong></td>
    <td colspan="1" style="text-align: right;">{{number_format($vat['summary']['net_amount'], 2, ',', '')}}</td>
</tr>
@foreach($vat as $key => $value)
    @if($key !== 'summary')
        <tr class="total">
            <td colspan="3"></td>
            <td colspan="3"><strong>VAT {{$key}}:</strong></td>
            <td colspan="1" style="text-align: right;">{{number_format($value['vat_amount'], 2, ',', '')}}</td>
        </tr>
    @endif

@endforeach
<tr class="total">
    <td colspan="3"></td>
    <td colspan="3"><strong>Wartość brutto:</strong></td>
    <td colspan="1" style="text-align: right;">{{number_format($vat['summary']['gross_amount'], 2, ',', '')}}</td>
</tr>
</table>
