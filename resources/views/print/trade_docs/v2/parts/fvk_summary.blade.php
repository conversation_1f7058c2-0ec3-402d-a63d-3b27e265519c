<table class="summary-half" width="80mm;">
    <tr>
        @if($vat['summary']['gross_amount'] > 0)
            @if($record->is_paid)
                <td><strong>Zapłacono:</strong></td>
                <td>
                    {{number_format($vat['summary']['gross_amount'] ?? 0, 2, ',', '')}} {{$record->currency}},
                    {{$record->payment_date->format('Y-m-d')}}
                </td>
            @else
                <td><strong>Do zapłaty:</strong></td>
                <td>
                    {{number_format($vat['summary']['gross_amount'] ?? 0, 2, ',', '')}} {{$record->currency}},
                </td>
            @endif
        @else
            @if($record->is_paid)
                <td><strong>Zwrócono:</strong></td>
                <td>
                    {{number_format(abs($vat['summary']['gross_amount'] ?? 0), 2, ',', '')}} {{$record->currency}},
                    {{$record->payment_date->format('Y-m-d')}}
                </td>
            @else
                <td><strong>Do zwrotu:</strong></td>
                <td>
                    {{number_format(abs($vat['summary']['gross_amount'] ?? 0), 2, ',', '')}} {{$record->currency}},
                </td>
            @endif
        @endif
    </tr>
</table>
<table class="summary" width="80mm;">
    <tr>
        <td style="width: 4cm;">
            <strong>Przyczyna korekty: </strong>
        </td>
        <td>
            {!! $record->notes !!}
        </td>
    </tr>
</table>
