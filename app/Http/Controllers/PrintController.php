<?php

namespace App\Http\Controllers;

use App\Enums\DocumentGeneralTypes;
use App\Enums\DocumentTypes;
use App\Models\JobTask;
use App\Models\PurchaseDoc;
use App\Models\WarehouseDoc;
use App\Repositories\TradeDocsRepository;
use App\Repositories\WarehouseDocsRepository;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;

class PrintController extends Controller
{

    public function print(Request $request, $doctype, $doc, $output)
    {
        return match (strtolower($doctype)) {
            strtolower(DocumentGeneralTypes::WAREHOUSE->value) => $this->pdf($request, $doc),
            strtolower(DocumentGeneralTypes::TRADE->value) => $this->tradeDoc($doc, $output),
            strtolower(DocumentGeneralTypes::PURCHASE->value) => $this->purchaseDoc($doc, $output),
            'zz' => $this->htmlDoc($request, $doctype, $doc),
            default => response('Not found', 404)
        };
    }

    public function tradeDoc($doc, $output)
    {
        $record = TradeDocsRepository::getDocByTransaction($doc);
        abort_if(empty($record), 404);

        $record->loadMissing(['items']);
        dd(tenant()->getInvoiceConfiguration()->selected_template);
        $variant = 'v2';
        $template = strtolower($record->type->name);
        $viewPath = 'print.trade_docs.' . $variant . '.' . $template;

        if ($output === 'html') {
            return view($viewPath, [
                'record' => $record,
                'document' => $record->getMeta(),
            ]);
        }

        $pdf = Pdf::loadView($viewPath, [
            'record' => $record,
            'document' => $record->getMeta(),
        ]);

        $name = Str::of($record->full_doc_number)->replace('/', '_')->title()->snake();
        return $pdf->download($name . '.pdf');
    }

    public function purchaseDoc($doc, $output)
    {
        $record = PurchaseDoc::find($doc);
        abort_if(empty($record), 404);

        $record->loadMissing(['items']);

        $variant = 'l2';
//        $template = 'fzv_' . $variant;
        $template = strtolower($record->type->name) . '_' . $variant;

        if ($output === 'html') {
            return view('print.purchase_docs.' . $template, [
                'record' => $record,
                'document' => $record->getMeta(),
            ]);
        }

        $pdf = Pdf::loadView('print.purchase_docs.' . $template, [
            'record' => $record,
            'document' => $record->getMeta(),
        ]);

        $name = Str::of($record->full_doc_number)->replace('/', '_')->title()->snake();
        return $pdf->download($name . '.pdf');
    }


    public function pdf(Request $request, $doc)
    {
//        dd($request->all());
        $record = WarehouseDocsRepository::GetDocByTransactionId($doc);
        abort_if(empty($record), 404);
        $ex_data = Session::pull('print.' . $record->transaction_id);
        Session::remove('print.' . $record->transaction_id);

        $doc_suffix = strtolower($record->type->name);

        $data = [
            'record' => $record,
            'print_note' => false
        ];

        if (($ex_data['print_note'] ?? false) && !empty($ex_data['note'])) {
            $data['note'] = $ex_data['note'];
            $data['print_note'] = true;
        }

        switch ($record->type) {
            case DocumentTypes::MW:
                $tr_id = str_replace('MW-', 'MP-', $record->transaction_id);
                $complimentary = WarehouseDocsRepository::GetDocByTransactionId($tr_id);
                if (empty($complimentary)) {
                    $complimentary = new WarehouseDoc();
                }
                $data['complimentary'] = $complimentary;
                $data['related_doc'] = $tr_id;
                break;
            case DocumentTypes::MP:
                $tr_id = str_replace('MP-', 'MW-', $record->transaction_id);
                $complimentary = WarehouseDocsRepository::GetDocByTransactionId($tr_id);
                if (empty($complimentary)) {
                    $complimentary = new WarehouseDoc();
                }
                $data['complimentary'] = $complimentary;
                $data['related_doc'] = $tr_id;
                break;
        }

        $pdf = Pdf::loadView('print.warehouse_docs.' . $doc_suffix, $data);

        return $pdf->stream($record->transaction_id . '.pdf');
    }

    public function html(Request $request, $doc)
    {
        $record = WarehouseDocsRepository::GetDocByTransactionId($doc);
        abort_if(empty($record), 404);

        $doc_suffix = strtolower($record->type->name);

        $data = [
            'record' => $record
        ];
        switch ($record->type) {
            case DocumentTypes::MW:
                $tr_id = str_replace('MW-', 'MP-', $record->transaction_id);
                $complimentary = WarehouseDocsRepository::GetDocByTransactionId($tr_id);
                if (empty($complimentary)) {
                    $complimentary = new WarehouseDoc();
                }
                $data['complimentary'] = $complimentary;
                $data['related_doc'] = $tr_id;
                break;
            case DocumentTypes::MP:
                $tr_id = str_replace('MP-', 'MW-', $record->transaction_id);
                $complimentary = WarehouseDocsRepository::GetDocByTransactionId($tr_id);
                if (empty($complimentary)) {
                    $complimentary = new WarehouseDoc();
                }
                $data['complimentary'] = $complimentary;
                $data['related_doc'] = $tr_id;
                break;
        }
        return view('print.warehouse_docs.' . $doc_suffix, $data);
    }

    public function htmlDoc(Request $request, string $type, int $doc)
    {
        $record = JobTask::find($doc);
        abort_if(empty($record), 404);

        $data = [
            'record' => $record
        ];
        return view('print.zz', $data);
    }

    protected function getTypeRepository(string $type)
    {
    }
}
