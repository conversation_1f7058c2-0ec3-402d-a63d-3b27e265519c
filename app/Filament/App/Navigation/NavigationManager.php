<?php

namespace App\Filament\App\Navigation;

use App\Enums\SystemModules;
use App\Filament\App\Pages\AdminStockList;
use App\Filament\App\Pages\Charts;
use App\Filament\App\Pages\CompanyProfile;
use App\Filament\App\Pages\EditProfile;
use App\Filament\App\Pages\InvoiceConfiguration;
use App\Filament\App\Pages\ProductHistory;
use App\Filament\App\Pages\ProductsList;
use App\Filament\App\Pages\StockList;
use App\Filament\App\Pages\Stocktaking;
use App\Filament\App\Pages\WarehouseState;
use App\Filament\App\Resources\DocumentSeriesPatternResource;
use App\Filament\App\Resources\JobTaskResource\Pages\ListJobTasks;
use App\Filament\App\Resources\JobTaskTemplateResource\Pages\ListJobTaskTemplates;
use App\Filament\App\Resources\ManufacturerResource;
use App\Filament\App\Resources\PartnerResource;
use App\Filament\App\Resources\ProductDemandResource;
use App\Filament\App\Resources\ProductsResource;
use App\Filament\App\Resources\PurchaseDocResource;
use App\Filament\App\Resources\TradeDocResource;
use App\Filament\App\Resources\UserResource;
use App\Filament\App\Resources\WarehouseDocResource\Pages\ListWarehouseDocs;
use App\Filament\App\Resources\WarehouseResource;
use Filament\Navigation\MenuItem;
use Filament\Navigation\NavigationBuilder;
use Filament\Navigation\NavigationGroup;
use Filament\Navigation\NavigationItem;
use Filament\Panel;
use Filament\Resources\Resource;
use Illuminate\Support\Str;

class NavigationManager
{

    public static function getNavigation(NavigationBuilder $builder): NavigationBuilder
    {

        if ((auth()->user()?->isTenantAdmin() ?? false) !== true) {
            return $builder->groups(self::getTenantEmployeeGroups($builder));
        }
        return $builder->groups(self::getTenantAdminGroups($builder));
    }

    protected static function getTenantAdminGroups(NavigationBuilder $builder): array
    {
        return [
            NavigationGroup::make('Main')
                ->items(self::getWarehouseMainNavigation())
                ->label(null)
                ->collapsible(false),
            NavigationGroup::make()
                ->collapsible(true)
                ->items(self::getWarehouseStateNavigation())
                ->when(fn() => tenant()?->hasModule(SystemModules::WAREHOUSE) ?? false)
                ->label(fn() => __('app._.warehouse_state')),
            NavigationGroup::make('Invoices')
                ->collapsible(true)
                ->items(self::getInvoicesNavigation())
                ->when(fn() => tenant()?->hasModule(SystemModules::INVOICES) ?? false)
                ->label('Faktury sprzedaży'),
            NavigationGroup::make('Faktury zakupu')
                ->collapsible(true)
                ->items(self::getPurchaseNavigation())
                ->when(fn() => tenant()?->hasModule(SystemModules::PURCHASE_INVOICES) ?? false)
                ->label('Faktury zakupu'),
            NavigationGroup::make()
                ->collapsible(true)
                ->items(self::getMyDataNavigation())
                ->label(fn() => 'Moje Dane'),
            NavigationGroup::make()
                ->collapsible(true)
                ->items(self::getJobTasksNavigation())
                ->label(fn() => 'Zlecenia'),
            NavigationGroup::make()
                ->collapsible(true)
                ->collapsed(true)
                ->items(self::getSettingNavigation())
                ->label(fn() => __('app._.settings')),
        ];
    }

    protected static function getTenantEmployeeGroups(NavigationBuilder $builder): array
    {
        return [
            NavigationGroup::make('Employee')
                ->items(self::getWarehouseEmployeeNavigation())
                ->label(null)
                ->collapsible(false),
            NavigationGroup::make()
                ->collapsible(true)
                ->collapsed(true)
                ->items(self::getEmployeeSettingNavigation())
                ->label(fn() => __('app._.settings')),
        ];
    }

    public static function getWarehouseEmployeeNavigation(): array
    {
        $list = [
            StockList::class,
            ListWarehouseDocs::class,
            ProductsList::class,
            ProductDemandResource::class,
        ];

        $items = [];

        foreach ($list as $class) {
            /**
             * @var Resource $class
             */
            if ($class::shouldRegisterNavigation()) {
                $items[] = NavigationItem::make($class::getNavigationLabel())
                    ->icon($class::getNavigationIcon())
                    ->isActiveWhen(fn(): bool => request()->url() === $class::getUrl())
                    ->url($class::getUrl());
            }
        }

        return $items;
    }

    public static function getWarehouseMainNavigation(): array
    {
        $list = [
            AdminStockList::class,
            ListWarehouseDocs::class,
            ProductHistory::class,
        ];


        $items = [];

        foreach ($list as $class) {
            /**
             * @var Resource $class
             */
            if ($class::shouldRegisterNavigation()) {
                $items[] = NavigationItem::make($class::getNavigationLabel())
                    ->icon($class::getNavigationIcon())
                    ->isActiveWhen(fn(): bool => request()->url() === $class::getUrl())
                    ->url($class::getUrl());
            }
        }

        return $items;
    }

    public static function getWarehouseStateNavigation(): array
    {
        $list = [
            Stocktaking::class,
            ProductDemandResource::class,
            WarehouseState::class,
        ];

        $items = [];

        foreach ($list as $class) {
            /**
             * @var Resource $class
             */
            if ($class::shouldRegisterNavigation()) {
                $items[] = NavigationItem::make($class::getNavigationLabel())
                    ->icon($class::getNavigationIcon())
                    ->isActiveWhen(fn(): bool => request()->url() === $class::getUrl())
                    ->url($class::getUrl());
            }
        }

        return $items;
    }

    public static function getInvoicesNavigation(): array
    {
        $list = [
            TradeDocResource::class,
            TradeDocResource\Pages\CreateTradeDoc::class,
        ];

        $items = [];

        foreach ($list as $class) {
            /**
             * @var Resource $class
             */
            if ($class::shouldRegisterNavigation()) {
                $items[] = NavigationItem::make($class::getNavigationLabel())
                    ->icon($class::getNavigationIcon())
                    ->isActiveWhen(fn(): bool => request()->url() === $class::getUrl())
                    ->url($class::getUrl());
            }
        }

        return $items;
    }

    public static function getPurchaseNavigation(): array
    {
        $list = [
            PurchaseDocResource\Pages\ListPurchaseDocs::class,
            PurchaseDocResource\Pages\CreatePurchaseDoc::class,
        ];

        $items = [];

        foreach ($list as $class) {
            /**
             * @var Resource $class
             */
            if ($class::shouldRegisterNavigation()) {
                $items[] = NavigationItem::make($class::getNavigationLabel())
                    ->icon($class::getNavigationIcon())
                    ->isActiveWhen(fn(): bool => Str::of(request()->url())->contains('purchase-docs'))
                    ->url($class::getUrl());
            }
        }

        return $items;
    }

    public static function getJobTasksNavigation(): array
    {
        $list = [
            ListJobTasks::class,
            ListJobTaskTemplates::class,
        ];

        $items = [];
        if (!(tenant()?->hasModule(SystemModules::JOB_TASKS) ?? false)) {
            return $items;
        }

        foreach ($list as $class) {
            /**
             * @var Resource $class
             */
            if ($class::shouldRegisterNavigation()) {
                $items[] = NavigationItem::make($class::getNavigationLabel())
                    ->label($class::getNavigationLabel())
                    ->icon($class::getNavigationIcon())
                    ->isActiveWhen(fn(): bool => request()->url() === $class::getUrl())
                    ->url($class::getUrl());
            }
        }

        return $items;
    }

    public static function getSettingNavigation()
    {
        $list = [
            CompanyProfile::class,
            InvoiceConfiguration::class,
            DocumentSeriesPatternResource::class,
            ManufacturerResource::class,
            PartnerResource::class,
            ProductsResource::class,
            UserResource::class,
            WarehouseResource::class,
        ];

        foreach ($list as $class) {
            /**
             * @var Resource $class
             */
            if ($class::shouldRegisterNavigation()) {
                $items[] = NavigationItem::make($class::getNavigationLabel())
                    ->icon($class::getNavigationIcon())
                    ->isActiveWhen(fn(): bool => request()->url() === $class::getUrl())
                    ->url($class::getUrl());
            }
        }
        return $items;
    }

    public static function getMyDataNavigation(): array
    {
        $list = [
            Charts::class,
        ];

        $items = [];

        foreach ($list as $class) {
            /**
             * @var Resource $class
             */
            if ($class::shouldRegisterNavigation()) {
                $items[] = NavigationItem::make($class::getNavigationLabel())
                    ->icon($class::getNavigationIcon())
                    ->isActiveWhen(fn(): bool => request()->url() === $class::getUrl())
                    ->url($class::getUrl());
            }
        }

        return $items;
    }

    public static function getEmployeeSettingNavigation()
    {
        $list = [
            PartnerResource::class,

        ];

        foreach ($list as $class) {
            /**
             * @var Resource $class
             */
            if ($class::shouldRegisterNavigation()) {
                $items[] = NavigationItem::make($class::getNavigationLabel())
                    ->icon($class::getNavigationIcon())
                    ->isActiveWhen(fn(): bool => request()->url() === $class::getUrl())
                    ->url($class::getUrl());
            }
        }
        return $items;
    }

    public static function getHomeLink(Panel $panel): string
    {
        if (tenant()?->hasModule(SystemModules::WAREHOUSE) ?? false) {
            return match (auth()->user()->isTenantAdmin()) {
                true => AdminStockList::getUrl(),
                default => StockList::getUrl(),
            };
        }
        return TradeDocResource::getUrl();
    }

    public static function getUserMenuNavigation(): array
    {
        return [
            'profile' => MenuItem::make()->url(fn(): string => EditProfile::getUrl())->label(
                fn() => 'Twój profil ' . auth()->user()?->profile?->fullName()
            ),
            'version' => MenuItem::make()->url('')
                ->label(
                    fn() => 'Wersja ' . config('app.version')
                )
            ->icon('heroicon-o-adjustments-vertical'),
        ];
    }

    public static function getDomain(string $panelName): ?string
    {
        return match ($panelName) {
            'app' => tenant()->system_domain ?? config('app.url'),
            default => null,
        };
    }
}
